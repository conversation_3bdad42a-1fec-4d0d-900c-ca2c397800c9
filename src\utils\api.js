// API configuration and helper functions
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// API endpoints
export const endpoints = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    profile: '/auth/profile'
  },
  products: {
    list: '/products',
    detail: (id) => `/products/${id}`,
    categories: '/products/categories',
    search: '/products/search'
  },
  cart: {
    get: '/cart',
    add: '/cart/add',
    update: '/cart/update',
    remove: '/cart/remove',
    clear: '/cart/clear'
  },
  orders: {
    create: '/orders',
    list: '/orders',
    detail: (id) => `/orders/${id}`
  },
  user: {
    profile: '/user/profile',
    updateProfile: '/user/profile',
    orders: '/user/orders',
    wishlist: '/user/wishlist'
  }
};

// HTTP client class
class ApiClient {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('authToken');
  }

  // Set authentication token
  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('authToken', token);
    } else {
      localStorage.removeItem('authToken');
    }
  }

  // Get authentication headers
  getAuthHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    return headers;
  }

  // Generic request method
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getAuthHeaders(),
      ...options
    };

    try {
      const response = await fetch(url, config);
      
      // Handle different response types
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.message || `HTTP error! status: ${response.status}`,
          response.status,
          errorData
        );
      }

      // Handle empty responses
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }
      
      return await response.text();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Network error occurred', 0, { originalError: error });
    }
  }

  // GET request
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    
    return this.request(url, {
      method: 'GET'
    });
  }

  // POST request
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // PUT request
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // PATCH request
  async patch(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data)
    });
  }

  // DELETE request
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    });
  }
}

// Custom error class for API errors
export class ApiError extends Error {
  constructor(message, status, data = {}) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

// Create API client instance
export const apiClient = new ApiClient();

// API service functions
export const authService = {
  login: async (credentials) => {
    const response = await apiClient.post(endpoints.auth.login, credentials);
    if (response.token) {
      apiClient.setToken(response.token);
    }
    return response;
  },

  register: async (userData) => {
    const response = await apiClient.post(endpoints.auth.register, userData);
    if (response.token) {
      apiClient.setToken(response.token);
    }
    return response;
  },

  logout: async () => {
    try {
      await apiClient.post(endpoints.auth.logout);
    } finally {
      apiClient.setToken(null);
    }
  },

  getProfile: () => apiClient.get(endpoints.auth.profile)
};

export const productService = {
  getProducts: (params = {}) => apiClient.get(endpoints.products.list, params),
  getProduct: (id) => apiClient.get(endpoints.products.detail(id)),
  getCategories: () => apiClient.get(endpoints.products.categories),
  searchProducts: (query, params = {}) => 
    apiClient.get(endpoints.products.search, { q: query, ...params })
};

export const cartService = {
  getCart: () => apiClient.get(endpoints.cart.get),
  addToCart: (productId, quantity = 1) => 
    apiClient.post(endpoints.cart.add, { productId, quantity }),
  updateCartItem: (itemId, quantity) => 
    apiClient.patch(endpoints.cart.update, { itemId, quantity }),
  removeFromCart: (itemId) => 
    apiClient.delete(`${endpoints.cart.remove}/${itemId}`),
  clearCart: () => apiClient.delete(endpoints.cart.clear)
};

export const orderService = {
  createOrder: (orderData) => apiClient.post(endpoints.orders.create, orderData),
  getOrders: (params = {}) => apiClient.get(endpoints.orders.list, params),
  getOrder: (id) => apiClient.get(endpoints.orders.detail(id))
};

export const userService = {
  getProfile: () => apiClient.get(endpoints.user.profile),
  updateProfile: (profileData) => 
    apiClient.put(endpoints.user.updateProfile, profileData),
  getOrders: (params = {}) => apiClient.get(endpoints.user.orders, params),
  getWishlist: () => apiClient.get(endpoints.user.wishlist)
};

// Utility functions
export const formatPrice = (price) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR'
  }).format(price);
};

export const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date));
};

export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

export default apiClient;
