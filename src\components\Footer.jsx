import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const Footer = () => {
  const quickLinks = [
    { path: '/', label: 'Home' },
    { path: '/products', label: 'Sneakers' },
    { path: '/about', label: 'About Us' },
    { path: '/contact', label: 'Contact' },
  ];

  const socialLinks = [
    { name: 'Instagram', href: 'https://www.instagram.com/thecometuniverse/', icon: '📷' },
    { name: 'LinkedIn', href: 'https://www.linkedin.com/company/thecometuniverse/', icon: '💼' },
    { name: 'WhatsApp', href: 'https://wa.me/919606081463', icon: '💬' },
  ];

  return (
    <motion.footer
      className="bg-slate-900 text-white relative overflow-hidden"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"></div>

      <div className="relative z-10 container-responsive py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <motion.div
            className="col-span-1 md:col-span-2"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h3 className="text-display text-2xl gradient-text mb-4">
              Comet
            </h3>
            <p className="text-slate-300 mb-6 max-w-md text-body leading-relaxed">
              Because Ordinary is Boring. A homegrown lifestyle sneaker brand where stellar craftsmanship meets captivating design.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.name}
                  href={social.href}
                  className="w-12 h-12 rounded-xl bg-slate-800 hover:bg-gradient-to-br hover:from-orange-500 hover:to-red-500 flex items-center justify-center transition-all duration-300 group"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  aria-label={social.name}
                >
                  <span className="text-xl group-hover:scale-110 transition-transform duration-300">{social.icon}</span>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h4 className="text-lg font-semibold mb-6 text-white">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.path}>
                  <Link
                    to={link.path}
                    className="text-slate-300 hover:text-orange-400 transition-all duration-300 hover:translate-x-1 inline-block"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <h4 className="text-lg font-semibold mb-6 text-white">Contact</h4>
            <div className="space-y-3 text-slate-300">
              <div className="flex items-center space-x-3 hover:text-orange-400 transition-colors duration-300">
                <span className="text-orange-400">📧</span>
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 hover:text-orange-400 transition-colors duration-300">
                <span className="text-orange-400">📞</span>
                <span>+1 (555) 123-4567</span>
              </div>
              <div className="flex items-start space-x-3 hover:text-orange-400 transition-colors duration-300">
                <span className="text-orange-400 mt-1">📍</span>
                <span>123 Future Street<br />Tech City, TC 12345</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          className="border-t border-slate-700 mt-12 pt-8 text-center"
          initial={{ y: 20, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <p className="text-slate-400 text-sm">
            &copy; 2024 Comet. All rights reserved. |
            <Link to="/privacy" className="hover:text-orange-400 ml-1 transition-colors duration-300">Privacy Policy</Link> |
            <Link to="/terms" className="hover:text-orange-400 ml-1 transition-colors duration-300">Terms of Service</Link>
          </p>
        </motion.div>
      </div>

      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tl from-blue-500/10 to-purple-500/10 rounded-full blur-xl"></div>
    </motion.footer>
  );
};

export default Footer;
