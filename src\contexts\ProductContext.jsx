import React, { createContext, useContext, useState, useEffect } from 'react';

const ProductContext = createContext();

export const useProducts = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
};

// Real Comet product data based on wearcomet.com
const COMET_PRODUCTS = [
  {
    id: 1,
    name: 'Alter ROGUE',
    collection: 'ALTER',
    price: 5299,
    originalPrice: 6299,
    images: [
      'https://www.wearcomet.com/cdn/shop/files/lateral_f9d408f7-c605-4d59-82a9-676c83cb0c73.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/medial_f9d408f7-c605-4d59-82a9-676c83cb0c73.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/heel_f9d408f7-c605-4d59-82a9-676c83cb0c73.jpg?v=**********&width=533',
    ],
    colors: ['Black', 'White', 'Navy'],
    sizes: ['6', '7', '8', '9', '10', '11', '12'],
    description: 'The Alter ROGUE combines street-ready aesthetics with premium comfort. Crafted for those who dare to be different.',
    features: [
      'Premium synthetic leather upper',
      'Memory foam insole',
      'Durable rubber outsole',
      'Breathable mesh lining',
    ],
    reviews: [
      {
        id: 1,
        name: 'Arjun M.',
        rating: 5,
        comment: 'Amazing quality and comfort. Perfect for daily wear.',
        date: '2024-01-15'
      },
      {
        id: 2,
        name: 'Priya S.',
        rating: 4,
        comment: 'Love the design! Fits perfectly.',
        date: '2024-01-10'
      }
    ],
    inStock: true,
    isNew: false,
    isBestSeller: true
  },
  {
    id: 2,
    name: 'Alter SONIC',
    collection: 'ALTER',
    price: 5299,
    originalPrice: 6299,
    images: [
      'https://www.wearcomet.com/cdn/shop/files/lateral_sonic.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/medial_sonic.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/heel_sonic.jpg?v=**********&width=533',
    ],
    colors: ['Electric Blue', 'Neon Green', 'Black'],
    sizes: ['6', '7', '8', '9', '10', '11', '12'],
    description: 'High-energy design meets superior performance. The Alter SONIC is built for speed and style.',
    features: [
      'Lightweight construction',
      'Enhanced grip outsole',
      'Moisture-wicking interior',
      'Reflective accents',
    ],
    reviews: [
      {
        id: 1,
        name: 'Rahul K.',
        rating: 5,
        comment: 'Perfect for workouts and casual wear!',
        date: '2024-01-20'
      }
    ],
    inStock: true,
    isNew: true,
    isBestSeller: false
  },
  {
    id: 3,
    name: 'X Lows TWISTER',
    collection: 'X LOWS',
    price: 4299,
    originalPrice: 5299,
    images: [
      'https://www.wearcomet.com/cdn/shop/files/lateral_twister.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/medial_twister.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/heel_twister.jpg?v=**********&width=533',
    ],
    colors: ['Storm Grey', 'Cloud White', 'Midnight Black'],
    sizes: ['6', '7', '8', '9', '10', '11', '12'],
    description: 'Low-top versatility with a twist. The X Lows TWISTER brings dynamic movement to your step.',
    features: [
      'Low-profile design',
      'Flexible sole technology',
      'Reinforced heel counter',
      'Premium canvas upper',
    ],
    reviews: [
      {
        id: 1,
        name: 'Sneha D.',
        rating: 4,
        comment: 'Comfortable and stylish. Great value for money.',
        date: '2024-01-12'
      }
    ],
    inStock: true,
    isNew: false,
    isBestSeller: false
  },
  {
    id: 4,
    name: 'X Lows PUMPKIN',
    collection: 'X LOWS',
    price: 4299,
    originalPrice: 5299,
    images: [
      'https://www.wearcomet.com/cdn/shop/files/lateral_pumpkin.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/medial_pumpkin.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/heel_pumpkin.jpg?v=**********&width=533',
    ],
    colors: ['Pumpkin Orange', 'Cream', 'Brown'],
    sizes: ['6', '7', '8', '9', '10', '11', '12'],
    description: 'Autumn vibes meet modern design. The X Lows PUMPKIN adds warmth to your wardrobe.',
    features: [
      'Seasonal color palette',
      'Soft suede accents',
      'Cushioned midsole',
      'Metal eyelets',
    ],
    reviews: [
      {
        id: 1,
        name: 'Vikram P.',
        rating: 5,
        comment: 'Unique color and excellent quality!',
        date: '2024-01-08'
      }
    ],
    inStock: true,
    isNew: false,
    isBestSeller: false
  },
  {
    id: 5,
    name: 'Aeon SALMON',
    collection: 'AEON',
    price: 4199,
    originalPrice: 4999,
    images: [
      'https://www.wearcomet.com/cdn/shop/files/lateral_salmon.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/medial_salmon.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/heel_salmon.jpg?v=**********&width=533',
    ],
    colors: ['Salmon Pink', 'Soft Grey', 'White'],
    sizes: ['6', '7', '8', '9', '10', '11', '12'],
    description: 'Timeless elegance with a modern twist. The Aeon SALMON brings sophistication to streetwear.',
    features: [
      'Premium leather construction',
      'Minimalist design',
      'Comfort foam padding',
      'Classic silhouette',
    ],
    reviews: [
      {
        id: 1,
        name: 'Anjali R.',
        rating: 5,
        comment: 'Beautiful color and perfect fit!',
        date: '2024-01-18'
      }
    ],
    inStock: true,
    isNew: false,
    isBestSeller: true
  },
  {
    id: 6,
    name: 'X Lows TIRAMISU',
    collection: 'X LOWS',
    price: 4299,
    originalPrice: 5299,
    images: [
      'https://www.wearcomet.com/cdn/shop/files/lateral_tiramisu.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/medial_tiramisu.jpg?v=**********&width=533',
      'https://www.wearcomet.com/cdn/shop/files/heel_tiramisu.jpg?v=**********&width=533',
    ],
    colors: ['Tiramisu Brown', 'Cream', 'Coffee'],
    sizes: ['6', '7', '8', '9', '10', '11', '12'],
    description: 'Sweet inspiration meets street style. The X Lows TIRAMISU is a treat for your feet.',
    features: [
      'Rich brown colorway',
      'Luxe texture details',
      'Comfort padding',
      'Durable construction',
    ],
    reviews: [
      {
        id: 1,
        name: 'Karan S.',
        rating: 4,
        comment: 'Love the unique name and design!',
        date: '2024-01-05'
      }
    ],
    inStock: false,
    isNew: true,
    isBestSeller: false
  }
];

export const ProductProvider = ({ children }) => {
  const [products, setProducts] = useState(COMET_PRODUCTS);
  const [filteredProducts, setFilteredProducts] = useState(COMET_PRODUCTS);
  const [filters, setFilters] = useState({
    collection: 'ALL',
    priceRange: [0, 10000],
    colors: [],
    sizes: [],
    inStock: false
  });
  const [sortBy, setSortBy] = useState('featured');
  const [searchQuery, setSearchQuery] = useState('');

  // Filter products based on current filters
  useEffect(() => {
    let filtered = [...products];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.collection.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Collection filter
    if (filters.collection !== 'ALL') {
      filtered = filtered.filter(product => product.collection === filters.collection);
    }

    // Price range filter
    filtered = filtered.filter(product => 
      product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]
    );

    // Color filter
    if (filters.colors.length > 0) {
      filtered = filtered.filter(product =>
        product.colors.some(color => filters.colors.includes(color))
      );
    }

    // Size filter
    if (filters.sizes.length > 0) {
      filtered = filtered.filter(product =>
        product.sizes.some(size => filters.sizes.includes(size))
      );
    }

    // Stock filter
    if (filters.inStock) {
      filtered = filtered.filter(product => product.inStock);
    }

    // Sort products
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'newest':
        filtered.sort((a, b) => b.isNew - a.isNew);
        break;
      default:
        // Featured - show bestsellers first, then new products
        filtered.sort((a, b) => {
          if (a.isBestSeller && !b.isBestSeller) return -1;
          if (!a.isBestSeller && b.isBestSeller) return 1;
          if (a.isNew && !b.isNew) return -1;
          if (!a.isNew && b.isNew) return 1;
          return 0;
        });
    }

    setFilteredProducts(filtered);
  }, [products, filters, sortBy, searchQuery]);

  const updateFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const clearFilters = () => {
    setFilters({
      collection: 'ALL',
      priceRange: [0, 10000],
      colors: [],
      sizes: [],
      inStock: false
    });
    setSearchQuery('');
  };

  const getProductById = (id) => {
    return products.find(product => product.id === parseInt(id));
  };

  const getCollections = () => {
    return [...new Set(products.map(product => product.collection))];
  };

  const getAllColors = () => {
    const colors = products.flatMap(product => product.colors);
    return [...new Set(colors)];
  };

  const getAllSizes = () => {
    const sizes = products.flatMap(product => product.sizes);
    return [...new Set(sizes)].sort((a, b) => parseInt(a) - parseInt(b));
  };

  const value = {
    products,
    filteredProducts,
    filters,
    updateFilters,
    clearFilters,
    sortBy,
    setSortBy,
    searchQuery,
    setSearchQuery,
    getProductById,
    getCollections,
    getAllColors,
    getAllSizes
  };

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  );
};
