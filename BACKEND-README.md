# Comet Backend API Server

A complete Node.js backend server for the Comet sneaker application with REST API endpoints, JWT authentication, and real product data.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Copy the package.json for backend
cp package-backend.json package.json

# Install dependencies
npm install
```

### 2. Start the Server

```bash
# Development mode with auto-reload
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3001`

## 📋 API Endpoints

### Product Management

#### Get All Products
```http
GET /api/products
```

**Query Parameters:**
- `category` (optional): `alter`, `x-lows`, `aeon`, or `all`
- `sort` (optional): `name`, `price-low`, `price-high`, `rating`
- `search` (optional): Search term for name, description, or colorway

**Examples:**
```bash
# Get all products
curl http://localhost:3001/api/products

# Filter by category and sort by price
curl "http://localhost:3001/api/products?category=alter&sort=price-low"

# Search for products
curl "http://localhost:3001/api/products?search=twister"
```

#### Get Product by ID
```http
GET /api/products/:id
```

**Example:**
```bash
curl http://localhost:3001/api/products/1
```

#### Get Categories
```http
GET /api/categories
```

### User Authentication

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Get User Profile (Protected)
```http
GET /api/auth/profile
Authorization: Bearer <jwt_token>
```

### Utility Endpoints

#### Health Check
```http
GET /api/health
```

#### Application Statistics
```http
GET /api/stats
```

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication:

1. Register or login to receive a JWT token
2. Include the token in the Authorization header: `Bearer <token>`
3. Tokens expire after 24 hours

## 📊 Real Product Data

The backend includes real Comet sneaker data:

- **Alter ROGUE** - ₹5,299 (Black Red) - NEW LAUNCH
- **Alter SONIC** - ₹5,299 (Blue Orange) - NEW LAUNCH
- **X Lows TWISTER** - ₹4,299 (Multi Purple) - BESTSELLER
- **X Lows PUMPKIN** - ₹4,299 (Black Orange)
- **Aeon SALMON** - ₹4,199 (Grey Pink)
- **X Lows TIRAMISU** - ₹4,299 (Brown Cream) - BESTSELLER

## 🛠️ Technical Features

- **Express.js** server with CORS enabled
- **JWT authentication** with bcrypt password hashing
- **Input validation** using express-validator
- **Error handling** middleware
- **Request logging** for debugging
- **In-memory storage** (no database required)
- **RESTful API** design with proper HTTP status codes

## 🔧 Configuration

Environment variables (optional):
- `PORT` - Server port (default: 3001)
- `JWT_SECRET` - JWT signing secret (default: auto-generated)
- `NODE_ENV` - Environment mode (development/production)

## 📝 Sample Frontend Integration

Update your frontend API configuration to use the backend:

```javascript
// In src/utils/api.js
const API_BASE_URL = 'http://localhost:3001/api';

// Example API calls
const getProducts = async () => {
  const response = await fetch(`${API_BASE_URL}/products`);
  return response.json();
};

const login = async (email, password) => {
  const response = await fetch(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  return response.json();
};
```

## 🚀 Production Deployment

For production deployment:

1. Set environment variables
2. Use a process manager like PM2
3. Set up a reverse proxy (nginx)
4. Use a real database instead of in-memory storage
5. Add rate limiting and security headers

## 🌟 Because Ordinary is Boring!

This backend server provides a complete foundation for the Comet sneaker application with real product data, secure authentication, and production-ready features.
