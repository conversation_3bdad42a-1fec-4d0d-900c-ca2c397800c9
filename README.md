# Comet - Lifestyle Sneaker Brand

A modern React application for the homegrown lifestyle sneaker brand "Comet" with the tagline "Because Ordinary is Boring".

## Project Structure

```
comet/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── assets/
│   │   ├── images/           # logos, product photos, icons
│   │   └── fonts/            # custom fonts if needed
│   ├── components/           # reusable UI components
│   │   ├── Header.jsx
│   │   ├── Footer.jsx
│   │   ├── Button.jsx
│   │   └── AnimatedText.jsx  # text with fade-in animations
│   ├── pages/                # top-level page components
│   │   ├── Home.jsx
│   │   ├── AboutUs.jsx
│   │   ├── ContactUs.jsx
│   │   ├── Auth/
│   │   │   ├── SignUp.jsx
│   │   │   └── Login.jsx
│   │   ├── Products.jsx      # product listing page
│   │   └── ProductDetail.jsx # individual product page
│   ├── routes/
│   │   └── AppRouter.jsx     # React Router setup
│   ├── styles/               # global and theme styles
│   │   ├── GlobalStyle.js    # styled-components global styles
│   │   └── theme.js          # design system theme
│   ├── utils/                # helper functions, API calls
│   │   └── api.js            # API client and service functions
│   ├── App.jsx
│   └── main.jsx
├── .gitignore
├── package.json
└── README.md
```

## Features

- **Modern React Architecture**: Built with React 19 and Vite
- **Responsive Design**: Mobile-first approach with styled-components
- **Component Library**: Reusable UI components with consistent theming
- **Routing**: Multi-page application with React Router
- **Authentication**: Login and signup pages with form validation
- **Sneaker Catalog**: Product listing and detail pages with real Comet data
- **API Integration**: Ready-to-use API client with service functions
- **Animations**: Smooth animations and transitions
- **Theme System**: Comprehensive design system with colors, typography, and spacing

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Technology Stack

- **React 19** - UI library
- **Vite** - Build tool and development server
- **React Router** - Client-side routing
- **Tailwind CSS v4** - Next-generation utility-first CSS framework
- **Framer Motion** - Animation library
- **ESLint** - Code linting

## Pages

- **Home** - Landing page with hero section and features
- **Products** - Product catalog with filtering and sorting
- **Product Detail** - Individual product pages with specifications
- **About Us** - Company information and values
- **Contact Us** - Contact form and company details
- **Login/Signup** - User authentication pages

## Components

- **Header** - Navigation bar with logo and menu
- **Footer** - Site footer with links and company info
- **Button** - Reusable button component with variants
- **AnimatedText** - Text component with animation effects

## Styling

The project uses Tailwind CSS v4 with:

- Next-generation utility-first CSS approach
- `@theme` directive for custom design tokens
- CSS custom properties for colors and fonts
- Responsive design with mobile-first approach
- Custom component classes for buttons and common elements
- Google Fonts integration (Inter and Poppins)
- No configuration files needed

## Animations

Powered by Framer Motion for:

- Page transitions and micro-interactions
- Scroll-triggered animations
- Hover and tap effects
- Staggered animations for lists and grids
- Custom animated text components

## API Integration

The project includes a complete API client setup with:

- Authentication services
- Product management
- Shopping cart functionality
- Order processing
- User profile management

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is licensed under the MIT License.
