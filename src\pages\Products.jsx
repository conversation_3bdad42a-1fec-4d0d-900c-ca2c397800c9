import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Button from '../components/Button';
import AnimatedText from '../components/AnimatedText';

const Products = () => {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  // Real product data from Comet website
  useEffect(() => {
    const cometProducts = [
      {
        id: 1,
        name: 'Alter ROGUE',
        category: 'alter',
        price: 5299,
        image: 'https://www.wearcomet.com/cdn/shop/files/lateral_f9d408f7-c605-4d59-82a9-676c83cb0c73.jpg?v=1752230759&width=533',
        description: 'Bold and edgy sneakers with a striking black and red colorway. Perfect for those who dare to be different.',
        features: ['Premium Materials', 'Comfortable Fit', 'Unique Design', 'Durable Construction'],
        colorway: 'Alter Black Red',
        sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        isNewLaunch: true,
        rating: 4.8,
        reviews: 124
      },
      {
        id: 2,
        name: 'Alter SONIC',
        category: 'alter',
        price: 5299,
        image: 'https://www.wearcomet.com/cdn/shop/files/LATERAL-min_ef28ca58-2c2d-47e6-8c35-81f2a9f39da8.jpg?v=1752230131&width=533',
        description: 'Dynamic blue and orange sneakers that embody speed and energy. Make a statement with every step.',
        features: ['Lightweight Design', 'Enhanced Comfort', 'Vibrant Colors', 'Superior Grip'],
        colorway: 'Alter Blue Orange',
        sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        isNewLaunch: true,
        rating: 4.7,
        reviews: 89
      },
      {
        id: 3,
        name: 'X Lows TWISTER',
        category: 'x-lows',
        price: 4299,
        image: 'https://www.wearcomet.com/cdn/shop/files/Lateral-min_cded1911-9e35-4454-a4f0-22ef9f587e10.jpg?v=1747304571&width=533',
        description: 'Multi-colored purple sneakers that twist expectations. A bestseller that combines style with comfort.',
        features: ['Versatile Style', 'All-Day Comfort', 'Eye-Catching Design', 'Quality Craftsmanship'],
        colorway: 'Multi Purple',
        sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        isBestseller: true,
        rating: 4.9,
        reviews: 167
      },
      {
        id: 4,
        name: 'X Lows PUMPKIN',
        category: 'x-lows',
        price: 4299,
        image: 'https://www.wearcomet.com/cdn/shop/files/lateral_1_-min_8413eac7-38c7-4ab3-b983-d28f1b33a45c.jpg?v=1737452927&width=533',
        description: 'Classic black and orange combination that never goes out of style. Perfect for any occasion.',
        features: ['Classic Design', 'Comfortable Sole', 'Premium Quality', 'Versatile Styling'],
        colorway: 'Black Orange',
        sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        rating: 4.6,
        reviews: 156
      },
      {
        id: 5,
        name: 'Aeon SALMON',
        category: 'aeon',
        price: 4199,
        image: 'https://www.wearcomet.com/cdn/shop/files/Lateral-view.jpg?v=1704174067&width=533',
        description: 'Elegant grey and pink sneakers that offer a perfect blend of sophistication and comfort.',
        features: ['Elegant Design', 'Soft Cushioning', 'Premium Materials', 'Stylish Appeal'],
        colorway: 'Grey Pink',
        sizes: [4, 5, 6, 7, 8, 9, 10, 11, 12],
        rating: 4.5,
        reviews: 78
      },
      {
        id: 6,
        name: 'X Lows TIRAMISU',
        category: 'x-lows',
        price: 4299,
        image: 'https://www.wearcomet.com/cdn/shop/files/tiramisu_lateral-min.jpg?v=1722317878&width=533',
        description: 'Delicious brown and cream colorway inspired by the classic dessert. A bestseller for good reason.',
        features: ['Unique Colorway', 'Comfortable Fit', 'High Quality', 'Trendy Design'],
        colorway: 'Lows Brown Cream',
        sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        isBestseller: true,
        rating: 4.8,
        reviews: 192
      }
    ];
    setProducts(cometProducts);
    setFilteredProducts(cometProducts);
  }, []);

  useEffect(() => {
    let filtered = products;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredProducts(filtered);
  }, [products, selectedCategory, sortBy]);

  const categories = [
    { value: 'all', label: 'All Sneakers' },
    { value: 'alter', label: 'ALTER Collection' },
    { value: 'x-lows', label: 'X LOWS Collection' },
    { value: 'aeon', label: 'AEON Collection' }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-responsive section-padding">
        {/* Hero Section */}
        <motion.section
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <AnimatedText
            text="Our Products"
            animationType="fade-in"
            className="text-4xl md:text-5xl font-bold font-heading text-gray-900 mb-4"
          />
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover the future of wearable technology
          </p>
        </motion.section>

        {/* Filters */}
        <motion.div
          className="bg-white rounded-lg shadow-md p-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex flex-col md:flex-row gap-6 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
              <div className="flex flex-col">
                <label htmlFor="category" className="text-sm font-medium text-gray-700 mb-2">
                  Category:
                </label>
                <select
                  id="category"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white"
                >
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex flex-col">
                <label htmlFor="sort" className="text-sm font-medium text-gray-700 mb-2">
                  Sort by:
                </label>
                <select
                  id="sort"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white"
                >
                  <option value="name">Name</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                </select>
              </div>
            </div>

            <div className="text-sm text-gray-600">
              {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found
            </div>
          </div>
        </motion.div>

        {/* Products Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredProducts.map(product => (
            <motion.div
              key={product.id}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
              variants={itemVariants}
              whileHover={{ y: -5 }}
              layout
            >
              <div className="aspect-w-16 aspect-h-12 bg-gray-200">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-48 object-cover"
                />
              </div>
              <div className="p-6 relative">
                {/* Badges */}
                {(product.isNewLaunch || product.isBestseller) && (
                  <div className="absolute top-4 right-4">
                    {product.isNewLaunch && (
                      <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        NEW LAUNCH
                      </span>
                    )}
                    {product.isBestseller && (
                      <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        BESTSELLER
                      </span>
                    )}
                  </div>
                )}

                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {product.name}
                </h3>
                <p className="text-sm text-gray-500 mb-2">
                  {product.colorway}
                </p>
                <p className="text-gray-600 mb-4 line-clamp-2">
                  {product.description}
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {product.features.slice(0, 2).map((feature, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                    >
                      {feature}
                    </span>
                  ))}
                </div>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className={i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'}>
                        ★
                      </span>
                    ))}
                  </div>
                  <span className="text-sm text-gray-600 ml-2">
                    {product.rating} ({product.reviews} reviews)
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-2xl font-bold text-gray-900">
                      ₹{product.price.toLocaleString()}
                    </span>
                    <p className="text-xs text-gray-500">MRP inclusive of taxes</p>
                  </div>
                  <div className="flex gap-2">
                    <Link to={`/products/${product.id}`}>
                      <Button variant="outline" size="small">View Details</Button>
                    </Link>
                    <Button variant="primary" size="small">Add to Cart</Button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* No Products Found */}
        {filteredProducts.length === 0 && (
          <motion.div
            className="text-center py-16"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No products found
            </h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your filters or search criteria
            </p>
            <Button
              variant="primary"
              onClick={() => {
                setSelectedCategory('all');
                setSortBy('name');
              }}
            >
              Reset Filters
            </Button>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Products;
