import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import ThemeToggle from './ThemeToggle';
import { useTheme } from '../contexts/ThemeContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  const { isDark } = useTheme();

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Products', path: '/products' },
    { name: 'About', path: '/about' },
    { name: 'Contact', path: '/contact' },
  ];

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu on route change
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  return (
    <motion.header
      className={`sticky top-0 z-50 transition-all duration-300 ${
        isScrolled
          ? `glass-effect shadow-xl border-b border-white/10`
          : `${isDark ? 'bg-slate-900' : 'bg-white'} shadow-sm border-b border-transparent`
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="container-responsive">
        <div className="flex justify-between items-center h-16 lg:h-20">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-2 group">
            <motion.div
              className="flex items-center gap-2 sm:gap-3"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                <span className="text-white font-bold text-lg sm:text-xl group-hover:scale-110 transition-transform duration-300">C</span>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl sm:text-2xl font-heading font-bold gradient-text group-hover:scale-105 transition-transform duration-300">
                  COMET
                </h1>
                <p className="text-xs text-muted font-medium -mt-1 group-hover:text-orange-400 transition-colors duration-300">Never Shy, Never Sorry</p>
              </div>
              
              <div className="block sm:hidden">
                <h1 className="text-xl font-heading font-bold gradient-text group-hover:scale-105 transition-transform duration-300">
                  COMET
                </h1>
              </div>
            </motion.div>
          </Link>
          <nav className="hidden lg:flex items-center gap-2">
            {navItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className="relative group"
              >
                <motion.div
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    location.pathname === item.path
                      ? 'text-primary bg-orange-50 dark:bg-orange-500/10'
                      : 'text-secondary hover:text-primary hover:bg-gray-50 dark:hover:bg-slate-800'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {item.name}
                  {location.pathname === item.path && (
                    <motion.div
                      className="absolute bottom-0 left-4 right-4 h-0.5 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"
                      layoutId="activeNav"
                      transition={{ type: "spring", stiffness: 380, damping: 30 }}
                    />
                  )}
                </motion.div>
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden lg:flex items-center gap-3">
            <ThemeToggle />

            <motion.div className="flex items-center gap-2">
              <Link
                to="/login"
                className="btn btn-ghost btn-sm"
              >
                Login
              </Link>
              <Link
                to="/signup"
                className="btn btn-primary btn-sm"
              >
                Sign Up
              </Link>
            </motion.div>
          </div>


        </div>


      </div>
    </motion.header>
  );
};

export default Header;
