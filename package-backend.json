{"name": "comet-backend", "version": "1.0.0", "description": "Backend API server for Comet sneaker application", "main": "backend.js", "scripts": {"start": "node backend.js", "dev": "nodemon backend.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["comet", "sneakers", "api", "nodejs", "express", "jwt", "bcrypt"], "author": "Comet Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}