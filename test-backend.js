#!/usr/bin/env node

/**
 * Simple test script for Comet Backend API
 * Run with: node test-backend.js
 */

const API_BASE = 'http://localhost:3001/api';

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  try {
    const url = `${API_BASE}${endpoint}`;
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    return { error: error.message };
  }
}

// Test functions
async function testHealthCheck() {
  console.log('🔍 Testing health check...');
  const result = await apiRequest('/health');
  console.log(`Status: ${result.status}`);
  console.log(`Response:`, result.data);
  console.log('');
}

async function testGetProducts() {
  console.log('👟 Testing get all products...');
  const result = await apiRequest('/products');
  console.log(`Status: ${result.status}`);
  console.log(`Total products: ${result.data?.total || 0}`);
  if (result.data?.data?.length > 0) {
    console.log(`First product: ${result.data.data[0].name} - ₹${result.data.data[0].price}`);
  }
  console.log('');
}

async function testGetProductById() {
  console.log('🔍 Testing get product by ID...');
  const result = await apiRequest('/products/1');
  console.log(`Status: ${result.status}`);
  if (result.data?.data) {
    console.log(`Product: ${result.data.data.name}`);
    console.log(`Price: ₹${result.data.data.price}`);
    console.log(`Category: ${result.data.data.category}`);
  }
  console.log('');
}

async function testFilterProducts() {
  console.log('🔽 Testing product filtering...');
  const result = await apiRequest('/products?category=alter&sort=price-low');
  console.log(`Status: ${result.status}`);
  console.log(`Filtered products: ${result.data?.total || 0}`);
  console.log('');
}

async function testGetCategories() {
  console.log('📂 Testing get categories...');
  const result = await apiRequest('/categories');
  console.log(`Status: ${result.status}`);
  console.log(`Categories: ${result.data?.data?.length || 0}`);
  if (result.data?.data) {
    result.data.data.forEach(cat => {
      console.log(`  - ${cat.name} (${cat.id})`);
    });
  }
  console.log('');
}

async function testUserRegistration() {
  console.log('👤 Testing user registration...');
  const testUser = {
    name: 'Test User',
    email: `test${Date.now()}@example.com`,
    password: 'password123'
  };
  
  const result = await apiRequest('/auth/register', {
    method: 'POST',
    body: JSON.stringify(testUser)
  });
  
  console.log(`Status: ${result.status}`);
  if (result.data?.success) {
    console.log(`User created: ${result.data.data.user.name}`);
    console.log(`Token received: ${result.data.data.token ? 'Yes' : 'No'}`);
    return result.data.data.token;
  } else {
    console.log(`Error: ${result.data?.message}`);
  }
  console.log('');
  return null;
}

async function testUserLogin() {
  console.log('🔐 Testing user login...');
  
  // First register a user
  const testUser = {
    name: 'Login Test User',
    email: `logintest${Date.now()}@example.com`,
    password: 'password123'
  };
  
  await apiRequest('/auth/register', {
    method: 'POST',
    body: JSON.stringify(testUser)
  });
  
  // Then try to login
  const loginResult = await apiRequest('/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: testUser.email,
      password: testUser.password
    })
  });
  
  console.log(`Status: ${loginResult.status}`);
  if (loginResult.data?.success) {
    console.log(`Login successful for: ${loginResult.data.data.user.name}`);
    return loginResult.data.data.token;
  } else {
    console.log(`Error: ${loginResult.data?.message}`);
  }
  console.log('');
  return null;
}

async function testProtectedRoute(token) {
  if (!token) {
    console.log('⚠️  Skipping protected route test (no token)');
    console.log('');
    return;
  }
  
  console.log('🔒 Testing protected route...');
  const result = await apiRequest('/auth/profile', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  console.log(`Status: ${result.status}`);
  if (result.data?.success) {
    console.log(`Profile retrieved for: ${result.data.data.name}`);
  } else {
    console.log(`Error: ${result.data?.message}`);
  }
  console.log('');
}

async function testStats() {
  console.log('📊 Testing stats endpoint...');
  const result = await apiRequest('/stats');
  console.log(`Status: ${result.status}`);
  if (result.data?.data) {
    const stats = result.data.data;
    console.log(`Total products: ${stats.totalProducts}`);
    console.log(`Total users: ${stats.totalUsers}`);
    console.log(`Average rating: ${stats.averageRating}`);
    console.log(`Price range: ₹${stats.priceRange.min} - ₹${stats.priceRange.max}`);
  }
  console.log('');
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Comet Backend API Tests');
  console.log('=====================================\n');
  
  // Check if server is running
  try {
    await fetch(`${API_BASE}/health`);
  } catch (error) {
    console.log('❌ Server is not running on http://localhost:3001');
    console.log('Please start the server with: node backend.js');
    process.exit(1);
  }
  
  // Run tests
  await testHealthCheck();
  await testGetProducts();
  await testGetProductById();
  await testFilterProducts();
  await testGetCategories();
  await testStats();
  
  const token = await testUserLogin();
  await testProtectedRoute(token);
  
  console.log('✅ All tests completed!');
  console.log('🌟 Because Ordinary is Boring!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, apiRequest };
