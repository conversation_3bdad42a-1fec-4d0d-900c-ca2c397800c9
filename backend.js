const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { body, validationResult, param, query } = require('express-validator');

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET || 'comet-secret-key-2025';

// Middleware
app.use(cors());
app.use(express.json());

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// In-memory storage
let users = [];
let nextUserId = 1;

// Real Comet product data
const products = [
  {
    id: 1,
    name: 'Alter ROGUE',
    category: 'alter',
    price: 5299,
    originalPrice: 5299,
    image: 'https://www.wearcomet.com/cdn/shop/files/lateral_f9d408f7-c605-4d59-82a9-676c83cb0c73.jpg?v=1752230759&width=533',
    images: [
      'https://www.wearcomet.com/cdn/shop/files/lateral_f9d408f7-c605-4d59-82a9-676c83cb0c73.jpg?v=1752230759&width=533',
      'https://www.wearcomet.com/cdn/shop/files/zoom_63f54157-520e-42ab-9960-0f31fcd01370.jpg?v=1752230759&width=533'
    ],
    description: 'Bold and edgy sneakers with a striking black and red colorway. Perfect for those who dare to be different.',
    features: ['Premium Materials', 'Comfortable Fit', 'Unique Design', 'Durable Construction'],
    colorway: 'Alter Black Red',
    sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    inStock: true,
    isNewLaunch: true,
    isBestseller: false,
    rating: 4.8,
    reviews: 124,
    specifications: {
      'Material': 'Premium synthetic leather and mesh',
      'Sole': 'Rubber outsole with enhanced grip',
      'Closure': 'Lace-up',
      'Care': 'Wipe with damp cloth',
      'Origin': 'Made in India'
    }
  },
  {
    id: 2,
    name: 'Alter SONIC',
    category: 'alter',
    price: 5299,
    originalPrice: 5299,
    image: 'https://www.wearcomet.com/cdn/shop/files/LATERAL-min_ef28ca58-2c2d-47e6-8c35-81f2a9f39da8.jpg?v=1752230131&width=533',
    images: [
      'https://www.wearcomet.com/cdn/shop/files/LATERAL-min_ef28ca58-2c2d-47e6-8c35-81f2a9f39da8.jpg?v=1752230131&width=533',
      'https://www.wearcomet.com/cdn/shop/files/zoom-min_3d2e4442-ab98-4a5a-8720-b645904254c3.jpg?v=1752230131&width=533'
    ],
    description: 'Dynamic blue and orange sneakers that embody speed and energy. Make a statement with every step.',
    features: ['Lightweight Design', 'Enhanced Comfort', 'Vibrant Colors', 'Superior Grip'],
    colorway: 'Alter Blue Orange',
    sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    inStock: true,
    isNewLaunch: true,
    isBestseller: false,
    rating: 4.7,
    reviews: 89,
    specifications: {
      'Material': 'Premium synthetic leather and mesh',
      'Sole': 'Rubber outsole with enhanced grip',
      'Closure': 'Lace-up',
      'Care': 'Wipe with damp cloth',
      'Origin': 'Made in India'
    }
  },
  {
    id: 3,
    name: 'X Lows TWISTER',
    category: 'x-lows',
    price: 4299,
    originalPrice: 4299,
    image: 'https://www.wearcomet.com/cdn/shop/files/Lateral-min_cded1911-9e35-4454-a4f0-22ef9f587e10.jpg?v=1747304571&width=533',
    images: [
      'https://www.wearcomet.com/cdn/shop/files/Lateral-min_cded1911-9e35-4454-a4f0-22ef9f587e10.jpg?v=1747304571&width=533',
      'https://www.wearcomet.com/cdn/shop/files/zoom-min_d58a2789-700c-4332-ba4a-ba83adafa1b0.jpg?v=1747304571&width=533'
    ],
    description: 'Multi-colored purple sneakers that twist expectations. A bestseller that combines style with comfort.',
    features: ['Versatile Style', 'All-Day Comfort', 'Eye-Catching Design', 'Quality Craftsmanship'],
    colorway: 'Multi Purple',
    sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    inStock: true,
    isNewLaunch: false,
    isBestseller: true,
    rating: 4.9,
    reviews: 167,
    specifications: {
      'Material': 'Canvas and synthetic leather',
      'Sole': 'Vulcanized rubber sole',
      'Closure': 'Lace-up',
      'Care': 'Machine washable (cold water)',
      'Origin': 'Made in India'
    }
  },
  {
    id: 4,
    name: 'X Lows PUMPKIN',
    category: 'x-lows',
    price: 4299,
    originalPrice: 4299,
    image: 'https://www.wearcomet.com/cdn/shop/files/lateral_1_-min_8413eac7-38c7-4ab3-b983-d28f1b33a45c.jpg?v=1737452927&width=533',
    images: [
      'https://www.wearcomet.com/cdn/shop/files/lateral_1_-min_8413eac7-38c7-4ab3-b983-d28f1b33a45c.jpg?v=1737452927&width=533',
      'https://www.wearcomet.com/cdn/shop/files/zoom_2_1_-min.jpg?v=1737452927&width=533'
    ],
    description: 'Classic black and orange combination that never goes out of style. Perfect for any occasion.',
    features: ['Classic Design', 'Comfortable Sole', 'Premium Quality', 'Versatile Styling'],
    colorway: 'Black Orange',
    sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    inStock: true,
    isNewLaunch: false,
    isBestseller: false,
    rating: 4.6,
    reviews: 156,
    specifications: {
      'Material': 'Canvas and synthetic leather',
      'Sole': 'Vulcanized rubber sole',
      'Closure': 'Lace-up',
      'Care': 'Machine washable (cold water)',
      'Origin': 'Made in India'
    }
  },
  {
    id: 5,
    name: 'Aeon SALMON',
    category: 'aeon',
    price: 4199,
    originalPrice: 4199,
    image: 'https://www.wearcomet.com/cdn/shop/files/Lateral-view.jpg?v=1704174067&width=533',
    images: [
      'https://www.wearcomet.com/cdn/shop/files/Lateral-view.jpg?v=1704174067&width=533',
      'https://www.wearcomet.com/cdn/shop/files/zoom-1.jpg?v=1704174067&width=533'
    ],
    description: 'Elegant grey and pink sneakers that offer a perfect blend of sophistication and comfort.',
    features: ['Elegant Design', 'Soft Cushioning', 'Premium Materials', 'Stylish Appeal'],
    colorway: 'Grey Pink',
    sizes: [4, 5, 6, 7, 8, 9, 10, 11, 12],
    inStock: true,
    isNewLaunch: false,
    isBestseller: false,
    rating: 4.5,
    reviews: 78,
    specifications: {
      'Material': 'Premium mesh and synthetic leather',
      'Sole': 'EVA midsole with rubber outsole',
      'Closure': 'Lace-up',
      'Care': 'Spot clean with mild detergent',
      'Origin': 'Made in India'
    }
  },
  {
    id: 6,
    name: 'X Lows TIRAMISU',
    category: 'x-lows',
    price: 4299,
    originalPrice: 4299,
    image: 'https://www.wearcomet.com/cdn/shop/files/tiramisu_lateral-min.jpg?v=1722317878&width=533',
    images: [
      'https://www.wearcomet.com/cdn/shop/files/tiramisu_lateral-min.jpg?v=1722317878&width=533',
      'https://www.wearcomet.com/cdn/shop/files/zoom-min_fd8c12ef-ce6f-408f-baa1-f615e228dd6f.jpg?v=1739212669&width=533'
    ],
    description: 'Delicious brown and cream colorway inspired by the classic dessert. A bestseller for good reason.',
    features: ['Unique Colorway', 'Comfortable Fit', 'High Quality', 'Trendy Design'],
    colorway: 'Lows Brown Cream',
    sizes: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    inStock: true,
    isNewLaunch: false,
    isBestseller: true,
    rating: 4.8,
    reviews: 192,
    specifications: {
      'Material': 'Canvas and synthetic leather',
      'Sole': 'Vulcanized rubber sole',
      'Closure': 'Lace-up',
      'Care': 'Machine washable (cold water)',
      'Origin': 'Made in India'
    }
  }
];

// Categories data
const categories = [
  {
    id: 'all',
    name: 'All Sneakers',
    description: 'Complete collection of Comet sneakers'
  },
  {
    id: 'alter',
    name: 'ALTER Collection',
    description: 'Bold and edgy designs for those who dare to be different'
  },
  {
    id: 'x-lows',
    name: 'X LOWS Collection',
    description: 'Versatile lifestyle sneakers for everyday wear'
  },
  {
    id: 'aeon',
    name: 'AEON Collection',
    description: 'Elegant and sophisticated sneakers with premium comfort'
  }
];

// Validation middleware
const validateErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

// JWT Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }
    req.user = user;
    next();
  });
};

// Helper function to generate JWT token
const generateToken = (user) => {
  return jwt.sign(
    { 
      id: user.id, 
      email: user.email,
      name: user.name 
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
};

// Error handling middleware
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
};

// ===== PRODUCT ROUTES =====

// GET /api/products - Get all products with filtering and sorting
app.get('/api/products', [
  query('category').optional().isIn(['all', 'alter', 'x-lows', 'aeon']),
  query('sort').optional().isIn(['name', 'price-low', 'price-high', 'rating']),
  query('search').optional().isString().trim(),
  validateErrors
], (req, res) => {
  try {
    let filteredProducts = [...products];
    const { category, sort, search } = req.query;

    // Filter by category
    if (category && category !== 'all') {
      filteredProducts = filteredProducts.filter(product => product.category === category);
    }

    // Search functionality
    if (search) {
      const searchTerm = search.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.colorway.toLowerCase().includes(searchTerm)
      );
    }

    // Sort products
    if (sort) {
      switch (sort) {
        case 'price-low':
          filteredProducts.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          filteredProducts.sort((a, b) => b.price - a.price);
          break;
        case 'rating':
          filteredProducts.sort((a, b) => b.rating - a.rating);
          break;
        case 'name':
        default:
          filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
          break;
      }
    }

    res.json({
      success: true,
      data: filteredProducts,
      total: filteredProducts.length,
      filters: {
        category: category || 'all',
        sort: sort || 'name',
        search: search || ''
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/products/:id - Get specific product
app.get('/api/products/:id', [
  param('id').isInt({ min: 1 }),
  validateErrors
], (req, res) => {
  try {
    const productId = parseInt(req.params.id);
    const product = products.find(p => p.id === productId);

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/products/categories - Get all categories
app.get('/api/categories', (req, res) => {
  try {
    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    next(error);
  }
});

// ===== AUTHENTICATION ROUTES =====

// POST /api/auth/register - User registration
app.post('/api/auth/register', [
  body('name').trim().isLength({ min: 2, max: 50 }).withMessage('Name must be between 2-50 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  validateErrors
], async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Check if user already exists
    const existingUser = users.find(user => user.email === email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create new user
    const newUser = {
      id: nextUserId++,
      name,
      email,
      password: hashedPassword,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    users.push(newUser);

    // Generate JWT token
    const token = generateToken(newUser);

    // Return user data without password
    const { password: _, ...userWithoutPassword } = newUser;

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: userWithoutPassword,
        token
      }
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/auth/login - User login
app.post('/api/auth/login', [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').notEmpty().withMessage('Password is required'),
  validateErrors
], async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Generate JWT token
    const token = generateToken(user);

    // Return user data without password
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userWithoutPassword,
        token
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/auth/profile - Get user profile (protected)
app.get('/api/auth/profile', authenticateToken, (req, res) => {
  try {
    // Find user by ID from token
    const user = users.find(u => u.id === req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Return user data without password
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: userWithoutPassword
    });
  } catch (error) {
    next(error);
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Comet API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// ===== ADDITIONAL ROUTES =====

// GET /api/stats - Get application statistics
app.get('/api/stats', (req, res) => {
  try {
    const stats = {
      totalProducts: products.length,
      totalUsers: users.length,
      productsByCategory: {
        alter: products.filter(p => p.category === 'alter').length,
        'x-lows': products.filter(p => p.category === 'x-lows').length,
        aeon: products.filter(p => p.category === 'aeon').length
      },
      newLaunches: products.filter(p => p.isNewLaunch).length,
      bestsellers: products.filter(p => p.isBestseller).length,
      averageRating: (products.reduce((sum, p) => sum + p.rating, 0) / products.length).toFixed(1),
      priceRange: {
        min: Math.min(...products.map(p => p.price)),
        max: Math.max(...products.map(p => p.price))
      }
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    next(error);
  }
});

// 404 handler for undefined routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.method} ${req.originalUrl} not found`,
    availableEndpoints: {
      products: [
        'GET /api/products',
        'GET /api/products/:id',
        'GET /api/categories'
      ],
      auth: [
        'POST /api/auth/register',
        'POST /api/auth/login',
        'GET /api/auth/profile'
      ],
      utility: [
        'GET /api/health',
        'GET /api/stats'
      ]
    }
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Comet API server running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📊 API Stats: http://localhost:${PORT}/api/stats`);
  console.log(`👟 Products: http://localhost:${PORT}/api/products`);
  console.log(`🔐 Auth endpoints: /api/auth/register, /api/auth/login`);
  console.log(`🌟 Because Ordinary is Boring!`);
  console.log(`\n📝 Sample requests:`);
  console.log(`   GET /api/products?category=alter&sort=price-low`);
  console.log(`   GET /api/products/1`);
  console.log(`   POST /api/auth/register (with name, email, password)`);
});

// Export for testing
module.exports = app;
