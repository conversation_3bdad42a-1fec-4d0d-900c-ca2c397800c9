import React from 'react';
import { motion } from 'framer-motion';
import Button from '../components/Button';

const DesignSystemTest = () => {
  return (
    <div className="min-h-screen section-padding">
      <div className="container-responsive">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-display text-responsive-2xl gradient-text mb-8 text-center">
            Design System Test
          </h1>
          
          {/* Button Variants */}
          <section className="mb-16">
            <h2 className="text-display text-responsive-xl text-primary mb-6">Button Variants</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button variant="primary" size="large">Primary Button</Button>
              <Button variant="secondary" size="large">Secondary Button</Button>
              <Button variant="outline" size="large">Outline Button</Button>
              <Button variant="ghost" size="large">Ghost Button</Button>
            </div>
          </section>

          {/* Card Styles */}
          <section className="mb-16">
            <h2 className="text-display text-responsive-xl text-primary mb-6">Card Styles</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="card-modern p-6">
                <h3 className="text-lg font-semibold text-primary mb-3">Modern Card</h3>
                <p className="text-secondary">This is a modern card with enhanced styling and hover effects.</p>
              </div>
              <div className="card-modern hover-lift p-6">
                <h3 className="text-lg font-semibold text-primary mb-3">Hover Lift Card</h3>
                <p className="text-secondary">This card has a lift effect on hover.</p>
              </div>
              <div className="card-modern hover-scale p-6">
                <h3 className="text-lg font-semibold text-primary mb-3">Hover Scale Card</h3>
                <p className="text-secondary">This card scales on hover.</p>
              </div>
            </div>
          </section>

          {/* Visual Effects */}
          <section className="mb-16">
            <h2 className="text-display text-responsive-xl text-primary mb-6">Visual Effects</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="card-modern p-6 glow-effect">
                <div className="w-16 h-16 bg-gradient-brand rounded-2xl flex items-center justify-center text-white mb-4">
                  ✨
                </div>
                <h3 className="text-lg font-semibold text-primary mb-3">Glow Effect</h3>
                <p className="text-secondary">Hover to see the glow effect.</p>
              </div>
              <div className="card-modern p-6">
                <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center text-white mb-4 float-animation">
                  🚀
                </div>
                <h3 className="text-lg font-semibold text-primary mb-3">Float Animation</h3>
                <p className="text-secondary">This icon has a floating animation.</p>
              </div>
              <div className="card-modern p-6 glass-effect">
                <div className="w-16 h-16 bg-gradient-secondary rounded-2xl flex items-center justify-center text-white mb-4">
                  💎
                </div>
                <h3 className="text-lg font-semibold text-primary mb-3">Glass Effect</h3>
                <p className="text-secondary">This card has a glass morphism effect.</p>
              </div>
            </div>
          </section>

          {/* Typography */}
          <section className="mb-16">
            <h2 className="text-display text-responsive-xl text-primary mb-6">Typography</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-display text-responsive-lg text-primary">Display Text</h3>
                <p className="text-body text-secondary">This is body text with relaxed line height for better readability.</p>
              </div>
              <div>
                <h4 className="gradient-text text-responsive-base font-semibold">Gradient Text</h4>
                <p className="text-muted text-responsive-sm">This is muted text in a smaller size.</p>
              </div>
            </div>
          </section>

          {/* Color Palette */}
          <section className="mb-16">
            <h2 className="text-display text-responsive-xl text-primary mb-6">Color Palette</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="w-full h-20 bg-gradient-primary rounded-xl mb-2"></div>
                <p className="text-sm text-secondary">Primary</p>
              </div>
              <div className="text-center">
                <div className="w-full h-20 bg-gradient-secondary rounded-xl mb-2"></div>
                <p className="text-sm text-secondary">Secondary</p>
              </div>
              <div className="text-center">
                <div className="w-full h-20 bg-gradient-brand rounded-xl mb-2"></div>
                <p className="text-sm text-secondary">Brand</p>
              </div>
              <div className="text-center">
                <div className="w-full h-20 bg-gradient-surface rounded-xl mb-2"></div>
                <p className="text-sm text-secondary">Surface</p>
              </div>
            </div>
          </section>
        </motion.div>
      </div>
    </div>
  );
};

export default DesignSystemTest;
