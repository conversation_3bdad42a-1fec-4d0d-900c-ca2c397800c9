import React from 'react';
import { motion } from 'framer-motion';
import AnimatedText from '../components/AnimatedText';

const AboutUs = () => {
  const values = [
    {
      icon: '🎨',
      title: 'Craftsmanship',
      description: 'Stellar craftsmanship is at the forefront of our philosophy. From the sole to the final shoe.'
    },
    {
      icon: '🚀',
      title: 'Never Shy, Never Sorry',
      description: 'Breaking away from the ordinary to create something extraordinary, just like a comet.'
    },
    {
      icon: '⭐',
      title: 'Quality',
      description: 'Every Comet that leaves our universe epitomizes thought, attention to detail and continuous effort.'
    },
    {
      icon: '🌟',
      title: 'Passion',
      description: 'Born solely out of our passion for sneakers and the excitement they bring.'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 via-white to-secondary-50 section-padding">
        <div className="container-responsive text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <AnimatedText
              text="About Comet"
              animationType="fade-in"
              className="text-4xl md:text-5xl font-bold font-heading text-gray-900 mb-6"
            />
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A homegrown lifestyle sneaker brand where stellar craftsmanship meets
              captivating concepts, creating shoes that are Never Shy, Never Sorry.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Content Sections */}
      <div className="container-responsive section-padding">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="space-y-16"
        >
          {/* Our Story */}
          <motion.section variants={itemVariants} className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold font-heading text-gray-900 mb-6">
                Our Story
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Comet is a homegrown, lifestyle sneaker brand. From the sole to the final shoe,
                stellar craftsmanship is at the forefront of our philosophy. Every Comet that
                leaves our universe to reach yours, epitomizes the thought, attention to detail
                and continuous effort that we put into it.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                We think of ourselves as experimental chefs, who pick out exceptional ingredients
                and let them shine through captivating concepts, all while upholding the highest
                quality and thus achieving the perfect balance.
              </p>
            </div>
            <div className="bg-gradient-to-br from-primary-100 to-secondary-100 rounded-2xl p-8 text-center">
              <div className="text-6xl mb-4">🌟</div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">Since 2024</h3>
              <p className="text-gray-600">Innovating the future of wearable technology</p>
            </div>
          </motion.section>

          {/* Our Mission */}
          <motion.section variants={itemVariants} className="grid md:grid-cols-2 gap-12 items-center">
            <div className="order-2 md:order-1 bg-gradient-to-br from-secondary-100 to-primary-100 rounded-2xl p-8 text-center">
              <div className="text-6xl mb-4">🎯</div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">Our Mission</h3>
              <p className="text-gray-600">Enhancing human potential through technology</p>
            </div>
            <div className="order-1 md:order-2">
              <h2 className="text-3xl font-bold font-heading text-gray-900 mb-6">
                Our Mission
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                We marvel at Space - its stars, planets, and the mighty sun radiate magnificence.
                Yet, in space, it is routine that rules. Planets orbit predictably and stars
                twinkle in silence, casting a monotonous aura over the cosmos.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                Amidst this repetitiveness, emerges an object as an unpredictable disruptor,
                blazing down its own path through the darkness - a COMET. Unstoppable and
                awe-inspiring, it transforms the night sky into a spectacle of wonder.
              </p>
            </div>
          </motion.section>

          {/* Our Values */}
          <motion.section variants={itemVariants} className="text-center">
            <h2 className="text-3xl font-bold font-heading text-gray-900 mb-12">
              Our Values
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => (
                <motion.div
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                  whileHover={{ y: -5 }}
                  variants={itemVariants}
                >
                  <div className="text-4xl mb-4">{value.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {value.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {value.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Our Team */}
          <motion.section variants={itemVariants} className="text-center bg-gray-50 rounded-2xl p-12">
            <h2 className="text-3xl font-bold font-heading text-gray-900 mb-6">
              Note from the Founders
            </h2>
            <p className="text-lg text-gray-600 leading-relaxed max-w-4xl mx-auto mb-8">
              "We have done the rounds in the corporate world. Valuable experiences indeed,
              but they never really got us going. Both of us got sucked in by the excitement
              of returning to India from Chicago and Boston to start something new, something
              we were super passionate about: <strong>sneakers.</strong>"
            </p>
            <p className="text-lg text-gray-600 leading-relaxed max-w-4xl mx-auto mb-8">
              "Many told us not to. Many still do. And many will continue to. But we both know
              sneakers bring a different energy out of us - a fire that we only dreamed of during
              our early days. For us, Comet is a brand born solely out of our passion."
            </p>
            <p className="text-lg text-gray-600 leading-relaxed max-w-4xl mx-auto mb-8">
              "We aim to push the envelope on design, on storytelling and on craftsmanship.
              Join us, support us, egg us on as we create the sneaker brand that India has long waited for!"
            </p>
            <p className="text-xl font-semibold text-gray-900">
              - Utkarsh & Dishant
            </p>
            <div className="flex justify-center gap-4 mt-8">
              <div className="bg-primary-100 rounded-full p-4">
                <span className="text-2xl">👟</span>
              </div>
              <div className="bg-secondary-100 rounded-full p-4">
                <span className="text-2xl">🇮🇳</span>
              </div>
              <div className="bg-primary-100 rounded-full p-4">
                <span className="text-2xl">🔥</span>
              </div>
            </div>
          </motion.section>
        </motion.div>
      </div>
    </div>
  );
};

export default AboutUs;
